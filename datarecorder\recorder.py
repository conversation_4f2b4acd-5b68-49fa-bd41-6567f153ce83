import time
import datetime
import copy
from threading import Thread
from queue import Empty, Queue

from qmt_xt import qmt_server
from database import recorder_client
from utils.object import TickData, BarData
from utils.utility import BarGenerator, generate_datetime
from utils.constant import Exchange, Interval, TickDirection

class RecodeServer:
    def __init__(self):
        self._active = False
        self.qmt_server = qmt_server
        self.full_minute = self.qmt_server.full_minute
        self.db_client = recorder_client
        self.tick_queue = Queue()
        self.minute_queue = Queue()
        self.bgs: dict[str, BarGenerator] = {}
        self.vt_symbols: list[str] = []
        self.last_tick_time = None
        self.thread_task = [
           Thread(target=self.save_tick),
           Thread(target=self.save_bar_minute)
        ]
        self.init()
        self.db_client.init_db()
    
    def init(self):
        self.vt_symbols = self.qmt_server.get_stock_list()
        # 绑定合约代码
        for vt_symbol in self.vt_symbols:
            def on_bar(bar: BarData) -> None:
                """"""
                pass
            self.bgs[vt_symbol] = BarGenerator(on_bar)
    
    def save_bar_minute(self):
        tick: TickData = None
        # 定义目标时间
        time_9_25 = datetime.time(9, 25)
        time_11_30 = datetime.time(11, 30)
        time_13_00 = datetime.time(13, 0)
        time_15_00 = datetime.time(15, 0)
        is_run = False
        while self._active:
            try:
                ticks = self.minute_queue.get(block=True, timeout=1)
                # 合成分钟数据
                for tick in ticks:
                    t_time = tick.datetime.time() 
                    if t_time < time_9_25 or t_time == time_13_00:
                        self.last_tick_time = tick.datetime
                        continue
                    if t_time == time_9_25:
                        tick = copy.deepcopy(tick)
                        tick.datetime = tick.datetime.replace(minute=29)
                        is_run = True
                    elif t_time in (time_11_30, time_15_00):
                        tick = copy.deepcopy(tick)
                        tick.datetime = tick.datetime - datetime.timedelta(milliseconds=10)
                        is_run = True
                    if (
                        self.last_tick_time
                        and self.last_tick_time.minute != tick.datetime.minute
                    ):
                        bars = {}
                        for vt_symbol, bg in self.bgs.items():
                            bars[vt_symbol] = bg.generate()
                        self.last_tick_time = tick.datetime

                        self.db_client.save_bar_data(
                            bars=[bar for bar in bars.values() if bar], interval=Interval.MINUTE
                            )
                    bg = self.bgs[tick.vt_symbol]
                    bg.update_tick(tick)
                    self.full_minute[tick.vt_symbol] = bg.bar
                # 处理几个特殊时间
                if is_run:
                    bars = {}
                    for vt_symbol, bg in self.bgs.items():
                        bars[vt_symbol] = bg.generate()
                    self.last_tick_time = tick.datetime
                    self.db_client.save_bar_data(
                        bars=[bar for bar in bars.values() if bar], interval=Interval.MINUTE
                        )
                    is_run = False

            except Empty:
                pass

    def save_tick(self):
        while self._active:
            try:
                ticks = self.tick_queue.get(block=True, timeout=1)
                self.minute_queue.put(ticks)
                self.db_client.save_tick_data(ticks=ticks)
            except Empty:
                pass

    def start(self, sleep_time: int = 60):
        self.qmt_server.subscribe_whole_quote()
        time.sleep(sleep_time)
        self.qmt_server.add_queue(self.tick_queue)
        self._active = True
        for t in self.thread_task:
            t.start()

    
    def close(self):
        self._active = False
        for t in self.thread_task:
            t.join()
        self.db_client.close()

