"""
Dash组件模块 - 包含各种自定义组件
"""

import dash
from dash import dcc, html, dash_table
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List

def create_stock_detail_panel(stock_data: Dict) -> html.Div:
    """创建股票详情面板"""
    if not stock_data:
        return html.Div("暂无数据", className="no-data")
    
    # 计算涨跌额和涨跌幅
    last_price = stock_data.get('last_price', 0)
    pre_close = stock_data.get('pre_close', 0)
    change = last_price - pre_close if pre_close > 0 else 0
    change_pct = (change / pre_close * 100) if pre_close > 0 else 0
    
    # 确定颜色
    color_class = "price-up" if change > 0 else "price-down" if change < 0 else "price-flat"
    
    return html.Div([
        # 基本信息
        html.Div([
            html.H3(f"{stock_data.get('name', '')} ({stock_data.get('symbol', '')})", 
                   className="stock-title"),
            html.Div([
                html.Span(f"¥{last_price:.2f}", className=f"current-price {color_class}"),
                html.Span(f"{change:+.2f} ({change_pct:+.2f}%)", 
                         className=f"price-change {color_class}")
            ], className="price-info"),
            html.Div([
                html.Span(f"开盘: ¥{stock_data.get('open', 0):.2f}", className="price-item"),
                html.Span(f"最高: ¥{stock_data.get('high', 0):.2f}", className="price-item"),
                html.Span(f"最低: ¥{stock_data.get('low', 0):.2f}", className="price-item"),
                html.Span(f"昨收: ¥{pre_close:.2f}", className="price-item"),
            ], className="price-details"),
            html.Div([
                html.Span(f"成交量: {stock_data.get('volume', 0):,}", className="volume-item"),
                html.Span(f"成交额: ¥{stock_data.get('amount', 0):,.0f}", className="volume-item"),
            ], className="volume-info"),
        ], className="basic-info"),
        
        # 五档买卖盘
        html.Div([
            html.H4("五档买卖盘", className="panel-subtitle"),
            create_order_book_table(stock_data)
        ], className="order-book-section"),
        
    ], className="stock-detail-panel")

def create_order_book_table(stock_data: Dict) -> dash_table.DataTable:
    """创建五档买卖盘表格"""
    bid_prices = stock_data.get('bid_prices', [0]*5)
    ask_prices = stock_data.get('ask_prices', [0]*5)
    bid_volumes = stock_data.get('bid_volumes', [0]*5)
    ask_volumes = stock_data.get('ask_volumes', [0]*5)
    
    # 构建表格数据
    data = []
    for i in range(5):
        data.append({
            'level': f'买{5-i}',
            'bid_volume': f"{bid_volumes[4-i]:,}" if bid_volumes[4-i] > 0 else "-",
            'bid_price': f"{bid_prices[4-i]:.2f}" if bid_prices[4-i] > 0 else "-",
            'ask_price': f"{ask_prices[i]:.2f}" if ask_prices[i] > 0 else "-",
            'ask_volume': f"{ask_volumes[i]:,}" if ask_volumes[i] > 0 else "-",
            'ask_level': f'卖{i+1}'
        })
    
    return dash_table.DataTable(
        data=data,
        columns=[
            {'name': '', 'id': 'level'},
            {'name': '买量', 'id': 'bid_volume'},
            {'name': '买价', 'id': 'bid_price'},
            {'name': '卖价', 'id': 'ask_price'},
            {'name': '卖量', 'id': 'ask_volume'},
            {'name': '', 'id': 'ask_level'},
        ],
        style_cell={
            'textAlign': 'center',
            'fontSize': '12px',
            'padding': '8px',
            'border': '1px solid #ddd'
        },
        style_header={
            'backgroundColor': 'rgb(230, 230, 230)',
            'fontWeight': 'bold'
        },
        style_data_conditional=[
            {
                'if': {'column_id': 'bid_price'},
                'backgroundColor': '#ffebee',
                'color': 'red'
            },
            {
                'if': {'column_id': 'ask_price'},
                'backgroundColor': '#e8f5e8',
                'color': 'green'
            }
        ]
    )

def create_mini_chart(df: pd.DataFrame, symbol: str) -> dcc.Graph:
    """创建迷你分时图"""
    if df.empty:
        return dcc.Graph(
            figure={
                'data': [],
                'layout': {
                    'title': '暂无数据',
                    'height': 200
                }
            }
        )
    
    fig = go.Figure()
    
    # 添加价格线
    fig.add_trace(go.Scatter(
        x=df['datetime'],
        y=df['close'],
        mode='lines',
        name='价格',
        line=dict(color='blue', width=2),
        fill='tonexty'
    ))
    
    fig.update_layout(
        title=f'{symbol} 分时图',
        height=200,
        margin=dict(l=20, r=20, t=40, b=20),
        showlegend=False,
        xaxis=dict(showgrid=False),
        yaxis=dict(showgrid=True, gridcolor='lightgray')
    )
    
    return dcc.Graph(figure=fig)

def create_market_summary_cards(market_data: List[Dict]) -> html.Div:
    """创建市场概览卡片"""
    if not market_data:
        return html.Div("暂无数据", className="no-data")
    
    # 计算市场统计
    total_stocks = len(market_data)
    up_stocks = len([d for d in market_data if d.get('change', 0) > 0])
    down_stocks = len([d for d in market_data if d.get('change', 0) < 0])
    flat_stocks = total_stocks - up_stocks - down_stocks
    
    # 计算平均涨跌幅
    avg_change_pct = np.mean([d.get('change_pct', 0) for d in market_data])
    
    return html.Div([
        html.Div([
            html.H4("市场概览", className="summary-title"),
            html.Div([
                create_summary_card("总股票数", total_stocks, "fas fa-chart-bar"),
                create_summary_card("上涨", up_stocks, "fas fa-arrow-up", "up"),
                create_summary_card("下跌", down_stocks, "fas fa-arrow-down", "down"),
                create_summary_card("平盘", flat_stocks, "fas fa-minus", "flat"),
            ], className="summary-cards"),
            html.Div([
                html.Span("平均涨跌幅: ", className="avg-label"),
                html.Span(f"{avg_change_pct:.2%}", 
                         className=f"avg-value {'up' if avg_change_pct > 0 else 'down' if avg_change_pct < 0 else 'flat'}")
            ], className="avg-change")
        ])
    ], className="market-summary")

def create_summary_card(title: str, value: int, icon: str, status: str = "") -> html.Div:
    """创建概览卡片"""
    return html.Div([
        html.Div([
            html.I(className=icon),
        ], className="card-icon"),
        html.Div([
            html.H5(title, className="card-title"),
            html.H3(str(value), className=f"card-value {status}")
        ], className="card-content")
    ], className=f"summary-card {status}")

def create_top_gainers_losers(market_data: List[Dict]) -> html.Div:
    """创建涨跌幅排行榜"""
    if not market_data:
        return html.Div("暂无数据", className="no-data")
    
    # 按涨跌幅排序
    sorted_data = sorted(market_data, key=lambda x: x.get('change_pct', 0), reverse=True)
    
    # 取前5名和后5名
    top_gainers = sorted_data[:5]
    top_losers = sorted_data[-5:]
    
    return html.Div([
        html.Div([
            html.H4("涨幅榜", className="rank-title up"),
            html.Div([
                create_rank_item(stock, i+1, "up") 
                for i, stock in enumerate(top_gainers)
            ], className="rank-list")
        ], className="rank-section"),
        
        html.Div([
            html.H4("跌幅榜", className="rank-title down"),
            html.Div([
                create_rank_item(stock, i+1, "down") 
                for i, stock in enumerate(top_losers)
            ], className="rank-list")
        ], className="rank-section")
    ], className="rank-container")

def create_rank_item(stock: Dict, rank: int, direction: str) -> html.Div:
    """创建排行榜项目"""
    return html.Div([
        html.Span(str(rank), className="rank-number"),
        html.Span(stock.get('symbol', ''), className="rank-symbol"),
        html.Span(f"{stock.get('change_pct', 0):.2%}", 
                 className=f"rank-change {direction}")
    ], className="rank-item")

def create_sector_analysis(market_data: List[Dict]) -> dcc.Graph:
    """创建板块分析图表"""
    if not market_data:
        return dcc.Graph(figure={'data': [], 'layout': {'title': '暂无数据'}})
    
    # 模拟板块数据（实际应用中需要从数据库获取板块信息）
    sectors = ['银行', '地产', '科技', '医药', '消费', '能源']
    sector_data = []
    
    for sector in sectors:
        # 随机分配股票到板块
        sector_stocks = np.random.choice(market_data, size=min(3, len(market_data)), replace=False)
        avg_change = np.mean([stock.get('change_pct', 0) for stock in sector_stocks])
        sector_data.append({
            'sector': sector,
            'change_pct': avg_change,
            'count': len(sector_stocks)
        })
    
    df = pd.DataFrame(sector_data)
    
    fig = px.bar(
        df, 
        x='sector', 
        y='change_pct',
        title='板块涨跌幅',
        color='change_pct',
        color_continuous_scale=['green', 'yellow', 'red']
    )
    
    fig.update_layout(
        height=300,
        margin=dict(l=20, r=20, t=40, b=20),
        showlegend=False
    )
    
    return dcc.Graph(figure=fig)

# CSS样式字符串
COMPONENT_STYLES = """
.stock-detail-panel {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.stock-title {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.5em;
}

.price-info {
    margin-bottom: 15px;
}

.current-price {
    font-size: 2em;
    font-weight: bold;
    margin-right: 15px;
}

.price-change {
    font-size: 1.2em;
}

.price-up {
    color: #f44336;
}

.price-down {
    color: #4caf50;
}

.price-flat {
    color: #666;
}

.price-details, .volume-info {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.price-item, .volume-item {
    font-size: 0.9em;
    color: #666;
}

.panel-subtitle {
    color: #333;
    margin: 20px 0 10px 0;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

.summary-cards {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.summary-card {
    flex: 1;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

.summary-card.up {
    border-left: 4px solid #f44336;
}

.summary-card.down {
    border-left: 4px solid #4caf50;
}

.summary-card.flat {
    border-left: 4px solid #666;
}

.card-icon {
    font-size: 1.5em;
    color: #667eea;
}

.card-content {
    flex: 1;
}

.card-title {
    margin: 0;
    font-size: 0.9em;
    color: #666;
}

.card-value {
    margin: 5px 0 0 0;
    font-size: 1.5em;
    font-weight: bold;
}

.card-value.up {
    color: #f44336;
}

.card-value.down {
    color: #4caf50;
}

.rank-container {
    display: flex;
    gap: 20px;
}

.rank-section {
    flex: 1;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rank-title {
    margin: 0 0 15px 0;
    font-size: 1.2em;
}

.rank-title.up {
    color: #f44336;
}

.rank-title.down {
    color: #4caf50;
}

.rank-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.rank-number {
    width: 30px;
    text-align: center;
    font-weight: bold;
    color: #667eea;
}

.rank-symbol {
    flex: 1;
    margin-left: 10px;
}

.rank-change {
    font-weight: bold;
}

.rank-change.up {
    color: #f44336;
}

.rank-change.down {
    color: #4caf50;
}

.no-data {
    text-align: center;
    color: #666;
    padding: 40px;
    font-style: italic;
}
"""
