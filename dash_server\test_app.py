"""
简化测试版本 - 验证基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

try:
    import dash
    from dash import dcc, html
    import plotly.graph_objects as go
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta
    
    print("✓ 所有依赖导入成功")
    
    # 创建简单的测试应用
    app = dash.Dash(__name__)
    
    # 生成测试数据
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), periods=30, freq='D')
    prices = np.random.randn(30).cumsum() + 100
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=dates, y=prices, mode='lines', name='测试数据'))
    fig.update_layout(title='测试图表', xaxis_title='日期', yaxis_title='价格')
    
    app.layout = html.Div([
        html.H1("金融量化展示平台 - 测试版"),
        html.P("这是一个简化的测试版本，用于验证基本功能。"),
        dcc.Graph(figure=fig)
    ])
    
    if __name__ == '__main__':
        print("🚀 启动测试应用...")
        print("📊 访问地址: http://localhost:8051")
        app.run_server(debug=True, host='0.0.0.0', port=8051)
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请安装必要的依赖包")
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
