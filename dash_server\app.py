"""
金融量化展示平台 - Dash应用主文件
"""

import dash
from dash import dcc, html, Input, Output, State, callback_context, dash_table
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from database.taosws_database import TaoswsDatabase
from utils.constant import Exchange, Interval
from utils.object import BarData, TickData
from data_service import data_service
from components import (
    create_stock_detail_panel,
    create_market_summary_cards,
    create_top_gainers_losers,
    create_sector_analysis,
    COMPONENT_STYLES
)

# 初始化Dash应用
app = dash.Dash(__name__, suppress_callback_exceptions=True)
app.title = "金融量化展示平台"

# 全局样式
external_stylesheets = [
    'https://codepen.io/chriddyp/pen/bWLwgP.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
]

# 应用布局
app.layout = html.Div([
    # 标题栏
    html.Div([
        html.H1("金融量化展示平台", className="header-title"),
        html.Div([
            html.I(className="fas fa-chart-line"),
            html.Span("实时行情分析系统", className="header-subtitle")
        ], className="header-subtitle-container")
    ], className="header"),
    
    # 主要内容区域
    html.Div([
        # 左侧控制面板
        html.Div([
            html.H3("控制面板", className="panel-title"),
            
            # 股票选择
            html.Div([
                html.Label("选择股票:", className="control-label"),
                dcc.Dropdown(
                    id='stock-selector',
                    options=[
                        {'label': '平安银行 (000001.SZ)', 'value': '000001.SZ'},
                        {'label': '万科A (000002.SZ)', 'value': '000002.SZ'},
                        {'label': '中国平安 (601318.SH)', 'value': '601318.SH'},
                        {'label': '贵州茅台 (600519.SH)', 'value': '600519.SH'},
                        {'label': '招商银行 (600036.SH)', 'value': '600036.SH'},
                    ],
                    value='000001.SZ',
                    className="control-dropdown"
                )
            ], className="control-group"),
            
            # 时间周期选择
            html.Div([
                html.Label("时间周期:", className="control-label"),
                dcc.Dropdown(
                    id='interval-selector',
                    options=[
                        {'label': '分钟线', 'value': 'minute'},
                        {'label': '日线', 'value': 'day'},
                        {'label': '周线', 'value': 'week'},
                    ],
                    value='day',
                    className="control-dropdown"
                )
            ], className="control-group"),
            
            # 日期范围选择
            html.Div([
                html.Label("日期范围:", className="control-label"),
                dcc.DatePickerRange(
                    id='date-picker-range',
                    start_date=(datetime.now() - timedelta(days=30)).date(),
                    end_date=datetime.now().date(),
                    display_format='YYYY-MM-DD',
                    className="control-date-picker"
                )
            ], className="control-group"),
            
            # 刷新按钮
            html.Div([
                html.Button(
                    [html.I(className="fas fa-sync-alt"), " 刷新数据"],
                    id='refresh-button',
                    className="btn btn-primary",
                    n_clicks=0
                )
            ], className="control-group"),
            
            # 技术指标选择
            html.Div([
                html.Label("技术指标:", className="control-label"),
                dcc.Checklist(
                    id='indicator-selector',
                    options=[
                        {'label': 'MA5', 'value': 'ma5'},
                        {'label': 'MA10', 'value': 'ma10'},
                        {'label': 'MA20', 'value': 'ma20'},
                        {'label': 'MACD', 'value': 'macd'},
                        {'label': 'RSI', 'value': 'rsi'},
                    ],
                    value=['ma5', 'ma10'],
                    className="control-checklist"
                )
            ], className="control-group"),
            
        ], className="left-panel"),
        
        # 中间主要内容
        html.Div([
            # 市场概览
            html.Div([
                html.Div(id='market-summary', children=[]),
            ], className="summary-section"),

            # K线图表区域
            html.Div([
                html.H3("K线图表", className="chart-title"),
                dcc.Loading(
                    id="loading-kline",
                    children=[
                        dcc.Graph(
                            id='kline-chart',
                            config={'displayModeBar': True, 'displaylogo': False}
                        )
                    ],
                    type="default"
                )
            ], className="chart-container"),

            # 实时行情表格
            html.Div([
                html.H3("实时行情", className="chart-title"),
                dcc.Loading(
                    id="loading-table",
                    children=[
                        dash_table.DataTable(
                            id='market-table',
                            columns=[
                                {'name': '股票代码', 'id': 'symbol'},
                                {'name': '股票名称', 'id': 'name'},
                                {'name': '最新价', 'id': 'last_price', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                                {'name': '涨跌额', 'id': 'change', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                                {'name': '涨跌幅', 'id': 'change_pct', 'type': 'numeric', 'format': {'specifier': '.2%'}},
                                {'name': '成交量', 'id': 'volume', 'type': 'numeric'},
                                {'name': '成交额', 'id': 'amount', 'type': 'numeric'},
                                {'name': '更新时间', 'id': 'datetime'},
                            ],
                            data=[],
                            sort_action="native",
                            filter_action="native",
                            page_action="native",
                            page_current=0,
                            page_size=20,
                            style_cell={'textAlign': 'center', 'fontSize': '12px'},
                            style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'},
                            style_data_conditional=[
                                {
                                    'if': {'filter_query': '{change} > 0'},
                                    'backgroundColor': '#ffebee',
                                    'color': 'red',
                                },
                                {
                                    'if': {'filter_query': '{change} < 0'},
                                    'backgroundColor': '#e8f5e8',
                                    'color': 'green',
                                }
                            ]
                        )
                    ],
                    type="default"
                )
            ], className="table-container"),

        ], className="middle-panel"),

        # 右侧信息面板
        html.Div([
            # 股票详情
            html.Div([
                html.H3("股票详情", className="panel-title"),
                html.Div(id='stock-detail', children=[])
            ], className="detail-section"),

            # 涨跌幅排行
            html.Div([
                html.H3("涨跌幅排行", className="panel-title"),
                html.Div(id='rank-section', children=[])
            ], className="rank-section-container"),

            # 板块分析
            html.Div([
                html.H3("板块分析", className="panel-title"),
                dcc.Loading(
                    id="loading-sector",
                    children=[
                        html.Div(id='sector-analysis', children=[])
                    ],
                    type="default"
                )
            ], className="sector-section"),

        ], className="right-panel")
        
    ], className="main-content"),
    
    # 存储组件
    dcc.Store(id='stock-data-store'),
    dcc.Store(id='market-data-store'),
    
    # 定时器
    dcc.Interval(
        id='interval-component',
        interval=30*1000,  # 30秒更新一次
        n_intervals=0
    )
    
], className="app-container")

# CSS样式
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <style>
            .app-container {
                font-family: 'Arial', sans-serif;
                margin: 0;
                padding: 0;
                background-color: #f5f5f5;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .header-title {
                margin: 0;
                font-size: 2.5em;
                font-weight: bold;
            }
            .header-subtitle-container {
                margin-top: 10px;
            }
            .header-subtitle {
                font-size: 1.2em;
                margin-left: 10px;
            }
            .main-content {
                display: flex;
                padding: 20px;
                gap: 20px;
                min-height: calc(100vh - 120px);
            }
            .left-panel {
                width: 300px;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                height: fit-content;
            }
            .middle-panel {
                flex: 2;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .right-panel {
                width: 350px;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .summary-section {
                margin-bottom: 20px;
            }
            .panel-title {
                color: #333;
                border-bottom: 2px solid #667eea;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            .control-group {
                margin-bottom: 20px;
            }
            .control-label {
                display: block;
                margin-bottom: 8px;
                font-weight: bold;
                color: #555;
            }
            .control-dropdown, .control-date-picker {
                width: 100%;
            }
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s;
            }
            .btn-primary {
                background-color: #667eea;
                color: white;
            }
            .btn-primary:hover {
                background-color: #5a6fd8;
            }
            .chart-container, .table-container {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            .chart-title {
                color: #333;
                margin-bottom: 15px;
                border-left: 4px solid #667eea;
                padding-left: 10px;
            }

            /* 组件样式 */
            .stock-detail-panel {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .current-price {
                font-size: 1.8em;
                font-weight: bold;
                margin-right: 15px;
            }
            .price-up { color: #f44336; }
            .price-down { color: #4caf50; }
            .price-flat { color: #666; }

            .summary-cards {
                display: flex;
                gap: 15px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }
            .summary-card {
                flex: 1;
                min-width: 120px;
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .summary-card.up { border-left: 4px solid #f44336; }
            .summary-card.down { border-left: 4px solid #4caf50; }
            .summary-card.flat { border-left: 4px solid #666; }

            .rank-container {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            .rank-section {
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .rank-item {
                display: flex;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #eee;
            }
            .rank-number {
                width: 30px;
                text-align: center;
                font-weight: bold;
                color: #667eea;
            }
            .rank-symbol {
                flex: 1;
                margin-left: 10px;
                font-size: 0.9em;
            }
            .rank-change {
                font-weight: bold;
                font-size: 0.9em;
            }
            .rank-change.up { color: #f44336; }
            .rank-change.down { color: #4caf50; }

            .detail-section, .rank-section-container, .sector-section {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .no-data {
                text-align: center;
                color: #666;
                padding: 40px;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# 回调函数
@app.callback(
    [Output('kline-chart', 'figure'),
     Output('market-table', 'data'),
     Output('stock-data-store', 'data'),
     Output('market-summary', 'children'),
     Output('stock-detail', 'children'),
     Output('rank-section', 'children'),
     Output('sector-analysis', 'children')],
    [Input('stock-selector', 'value'),
     Input('interval-selector', 'value'),
     Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date'),
     Input('indicator-selector', 'value'),
     Input('refresh-button', 'n_clicks'),
     Input('interval-component', 'n_intervals')]
)
def update_all_components(selected_stock, selected_interval, start_date, end_date,
                         selected_indicators, refresh_clicks, n_intervals):
    """更新所有组件"""

    # 获取K线数据
    kline_df = data_service.get_kline_data(
        symbol=selected_stock,
        interval=selected_interval,
        start_date=start_date,
        end_date=end_date
    )

    # 创建K线图
    kline_fig = create_kline_chart(kline_df, selected_stock, selected_indicators)

    # 获取实时行情数据
    market_data = data_service.get_tick_data(limit=50)

    # 获取股票详情
    stock_detail = data_service.get_stock_detail(selected_stock)

    # 创建各种组件
    market_summary = create_market_summary_cards(market_data)
    stock_detail_panel = create_stock_detail_panel(stock_detail)
    rank_panel = create_top_gainers_losers(market_data)
    sector_chart = create_sector_analysis(market_data)

    # 存储数据
    store_data = {
        'kline_data': kline_df.to_dict('records') if not kline_df.empty else [],
        'market_data': market_data,
        'selected_stock': selected_stock,
        'stock_detail': stock_detail
    }

    return (kline_fig, market_data, store_data, market_summary,
            stock_detail_panel, rank_panel, sector_chart)

def create_kline_chart(df, symbol, indicators):
    """创建K线图表"""
    if df.empty:
        return {
            'data': [],
            'layout': {
                'title': f'{symbol} - 暂无数据',
                'xaxis': {'title': '时间'},
                'yaxis': {'title': '价格'},
                'height': 600
            }
        }

    # 创建子图
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=(f'{symbol} K线图', '成交量', '技术指标'),
        row_heights=[0.6, 0.2, 0.2]
    )

    # K线图
    fig.add_trace(
        go.Candlestick(
            x=df['datetime'],
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name='K线',
            increasing_line_color='red',
            decreasing_line_color='green'
        ),
        row=1, col=1
    )

    # 添加移动平均线
    if 'ma5' in indicators and 'ma5' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['ma5'],
                mode='lines',
                name='MA5',
                line=dict(color='blue', width=1)
            ),
            row=1, col=1
        )

    if 'ma10' in indicators and 'ma10' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['ma10'],
                mode='lines',
                name='MA10',
                line=dict(color='orange', width=1)
            ),
            row=1, col=1
        )

    if 'ma20' in indicators and 'ma20' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['ma20'],
                mode='lines',
                name='MA20',
                line=dict(color='purple', width=1)
            ),
            row=1, col=1
        )

    # 成交量柱状图
    colors = ['red' if close >= open else 'green'
              for close, open in zip(df['close'], df['open'])]

    fig.add_trace(
        go.Bar(
            x=df['datetime'],
            y=df['volume'],
            name='成交量',
            marker_color=colors,
            opacity=0.7
        ),
        row=2, col=1
    )

    # 技术指标 - MACD或RSI
    if 'macd' in indicators and 'macd' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['macd'],
                mode='lines',
                name='MACD',
                line=dict(color='blue', width=1)
            ),
            row=3, col=1
        )

        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['macd_signal'],
                mode='lines',
                name='MACD Signal',
                line=dict(color='red', width=1)
            ),
            row=3, col=1
        )

    if 'rsi' in indicators and 'rsi' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df['datetime'],
                y=df['rsi'],
                mode='lines',
                name='RSI',
                line=dict(color='purple', width=1)
            ),
            row=3, col=1
        )

        # RSI超买超卖线
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)

    # 更新布局
    fig.update_layout(
        title=f'{symbol} 股票分析',
        xaxis_rangeslider_visible=False,
        height=800,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # 更新x轴
    fig.update_xaxes(title_text="时间", row=3, col=1)

    # 更新y轴
    fig.update_yaxes(title_text="价格", row=1, col=1)
    fig.update_yaxes(title_text="成交量", row=2, col=1)
    fig.update_yaxes(title_text="指标值", row=3, col=1)

    return fig

if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050)
