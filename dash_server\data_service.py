"""
数据服务模块 - 负责从数据库获取和处理金融数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import sys
from pathlib import Path

# 添加项目根目录到路径
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from database.taosws_database import TaoswsDatabase
from utils.constant import Exchange, Interval
from utils.object import BarData, TickData

class DataService:
    """数据服务类"""
    
    def __init__(self):
        """初始化数据服务"""
        try:
            self.db_client = TaoswsDatabase()
            self.cache = {}  # 简单的内存缓存
            self.cache_timeout = 300  # 缓存5分钟
            print("数据服务初始化成功")
        except Exception as e:
            print(f"数据服务初始化失败: {e}")
            self.db_client = None
    
    def get_stock_list(self) -> List[Dict]:
        """获取股票列表
        
        注意: 此方法需要数据库提供股票列表查询接口
        目前返回模拟数据，后续需要补充数据库查询方法
        """
        # TODO: 需要在数据库中添加股票列表查询方法
        mock_stocks = [
            {'symbol': '000001.SZ', 'name': '平安银行', 'exchange': 'SZ'},
            {'symbol': '000002.SZ', 'name': '万科A', 'exchange': 'SZ'},
            {'symbol': '600036.SH', 'name': '招商银行', 'exchange': 'SH'},
            {'symbol': '600519.SH', 'name': '贵州茅台', 'exchange': 'SH'},
            {'symbol': '601318.SH', 'name': '中国平安', 'exchange': 'SH'},
        ]
        return mock_stocks
    
    def get_kline_data(self, symbol: str, interval: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取K线数据"""
        if not self.db_client:
            return self._get_mock_kline_data(symbol, interval, start_date, end_date)
        
        try:
            # 解析股票代码和交易所
            symbol_code, exchange_code = symbol.split('.')
            exchange = Exchange.SH if exchange_code == 'SH' else Exchange.SZ
            
            # 转换时间周期
            interval_map = {
                'minute': Interval.MINUTE,
                'day': Interval.DAILY,
                'week': Interval.WEEKLY
            }
            interval_obj = interval_map.get(interval, Interval.DAILY)
            
            # 转换日期格式
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            # 从数据库获取数据
            bars = self.db_client.load_bar_data(
                symbol=symbol_code,
                exchange=exchange,
                interval=interval_obj,
                start=start_dt,
                end=end_dt
            )
            
            if not bars:
                print(f"未找到股票 {symbol} 的K线数据")
                return self._get_mock_kline_data(symbol, interval, start_date, end_date)
            
            # 转换为DataFrame
            data = []
            for bar in bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': bar.open,
                    'high': bar.high,
                    'low': bar.low,
                    'close': bar.close,
                    'volume': bar.volume,
                    'amount': bar.amount,
                    'pre_close': bar.pre_close
                })
            
            df = pd.DataFrame(data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime')
            
            # 计算技术指标
            df = self._calculate_indicators(df)
            
            return df
            
        except Exception as e:
            print(f"获取K线数据失败: {e}")
            return self._get_mock_kline_data(symbol, interval, start_date, end_date)
    
    def get_tick_data(self, symbol: str, limit: int = 100) -> List[Dict]:
        """获取实时tick数据"""
        if not self.db_client:
            return self._get_mock_tick_data()
        
        try:
            # 获取最新的tick数据
            ticks = self.db_client.get_full_tick()
            
            if not ticks:
                print("未获取到实时数据")
                return self._get_mock_tick_data()
            
            # 转换为标准格式
            result = []
            for tick_dict in ticks[:limit]:
                # 计算涨跌额和涨跌幅
                last_price = tick_dict.get('last_price', 0)
                pre_close = tick_dict.get('pre_close', 0)
                change = last_price - pre_close if pre_close > 0 else 0
                change_pct = change / pre_close if pre_close > 0 else 0
                
                result.append({
                    'symbol': tick_dict.get('vt_symbol', ''),
                    'name': self._get_stock_name(tick_dict.get('vt_symbol', '')),
                    'last_price': last_price,
                    'change': change,
                    'change_pct': change_pct,
                    'volume': tick_dict.get('volume', 0),
                    'amount': tick_dict.get('amount', 0),
                    'datetime': tick_dict.get('datetime', ''),
                    'high': tick_dict.get('high', 0),
                    'low': tick_dict.get('low', 0),
                    'open': tick_dict.get('open', 0),
                    'pre_close': pre_close,
                    'bid_price_1': tick_dict.get('bid_price_1', 0),
                    'ask_price_1': tick_dict.get('ask_price_1', 0),
                    'bid_volume_1': tick_dict.get('bid_volume_1', 0),
                    'ask_volume_1': tick_dict.get('ask_volume_1', 0),
                })
            
            return result
            
        except Exception as e:
            print(f"获取实时数据失败: {e}")
            return self._get_mock_tick_data()
    
    def get_stock_detail(self, symbol: str) -> Dict:
        """获取股票详细信息"""
        if not self.db_client:
            return self._get_mock_stock_detail(symbol)
        
        try:
            # 获取指定股票的最新tick数据
            tick_data = self.db_client.get_tick_by_vt_symbol(symbol)
            
            if not tick_data:
                print(f"未找到股票 {symbol} 的详细数据")
                return self._get_mock_stock_detail(symbol)
            
            # 取最新的一条数据
            latest_tick = tick_data[0] if tick_data else {}
            
            return {
                'symbol': symbol,
                'name': self._get_stock_name(symbol),
                'last_price': latest_tick.get('last_price', 0),
                'open': latest_tick.get('open', 0),
                'high': latest_tick.get('high', 0),
                'low': latest_tick.get('low', 0),
                'pre_close': latest_tick.get('pre_close', 0),
                'volume': latest_tick.get('volume', 0),
                'amount': latest_tick.get('amount', 0),
                'datetime': latest_tick.get('datetime', ''),
                'bid_prices': [
                    latest_tick.get('bid_price_1', 0),
                    latest_tick.get('bid_price_2', 0),
                    latest_tick.get('bid_price_3', 0),
                    latest_tick.get('bid_price_4', 0),
                    latest_tick.get('bid_price_5', 0),
                ],
                'ask_prices': [
                    latest_tick.get('ask_price_1', 0),
                    latest_tick.get('ask_price_2', 0),
                    latest_tick.get('ask_price_3', 0),
                    latest_tick.get('ask_price_4', 0),
                    latest_tick.get('ask_price_5', 0),
                ],
                'bid_volumes': [
                    latest_tick.get('bid_volume_1', 0),
                    latest_tick.get('bid_volume_2', 0),
                    latest_tick.get('bid_volume_3', 0),
                    latest_tick.get('bid_volume_4', 0),
                    latest_tick.get('bid_volume_5', 0),
                ],
                'ask_volumes': [
                    latest_tick.get('ask_volume_1', 0),
                    latest_tick.get('ask_volume_2', 0),
                    latest_tick.get('ask_volume_3', 0),
                    latest_tick.get('ask_volume_4', 0),
                    latest_tick.get('ask_volume_5', 0),
                ],
            }
            
        except Exception as e:
            print(f"获取股票详细信息失败: {e}")
            return self._get_mock_stock_detail(symbol)
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        if df.empty:
            return df
        
        # 移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        return df
    
    def _get_stock_name(self, symbol: str) -> str:
        """获取股票名称"""
        name_map = {
            '000001.SZ': '平安银行',
            '000002.SZ': '万科A',
            '600036.SH': '招商银行',
            '600519.SH': '贵州茅台',
            '601318.SH': '中国平安',
        }
        return name_map.get(symbol, symbol)
    
    def _get_mock_kline_data(self, symbol: str, interval: str, start_date: str, end_date: str) -> pd.DataFrame:
        """生成模拟K线数据"""
        print(f"使用模拟K线数据: {symbol}")
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 生成日期序列
        if interval == 'minute':
            freq = '1min'
            periods = int((end_dt - start_dt).total_seconds() / 60)
        elif interval == 'day':
            freq = '1D'
            periods = (end_dt - start_dt).days
        else:
            freq = '1W'
            periods = int((end_dt - start_dt).days / 7)
        
        periods = min(periods, 1000)  # 限制数据量
        
        dates = pd.date_range(start=start_dt, periods=periods, freq=freq)
        
        # 生成模拟价格数据
        np.random.seed(42)
        base_price = 10.0
        prices = []
        current_price = base_price
        
        for i in range(len(dates)):
            # 随机游走生成价格
            change = np.random.normal(0, 0.02)
            current_price = max(current_price * (1 + change), 0.1)
            
            open_price = current_price
            high_price = open_price * (1 + abs(np.random.normal(0, 0.01)))
            low_price = open_price * (1 - abs(np.random.normal(0, 0.01)))
            close_price = low_price + (high_price - low_price) * np.random.random()
            volume = np.random.randint(1000000, 10000000)
            
            prices.append({
                'datetime': dates[i],
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(close_price * volume, 2),
                'pre_close': round(open_price, 2)
            })
            
            current_price = close_price
        
        df = pd.DataFrame(prices)
        df = self._calculate_indicators(df)
        return df
    
    def _get_mock_tick_data(self) -> List[Dict]:
        """生成模拟实时数据"""
        print("使用模拟实时数据")
        
        stocks = self.get_stock_list()
        result = []
        
        for stock in stocks:
            base_price = np.random.uniform(8, 50)
            pre_close = base_price
            last_price = base_price * (1 + np.random.normal(0, 0.03))
            change = last_price - pre_close
            change_pct = change / pre_close
            
            result.append({
                'symbol': stock['symbol'],
                'name': stock['name'],
                'last_price': round(last_price, 2),
                'change': round(change, 2),
                'change_pct': change_pct,
                'volume': np.random.randint(1000000, 50000000),
                'amount': round(last_price * np.random.randint(1000000, 50000000), 2),
                'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'high': round(last_price * 1.05, 2),
                'low': round(last_price * 0.95, 2),
                'open': round(pre_close, 2),
                'pre_close': round(pre_close, 2),
            })
        
        return result
    
    def _get_mock_stock_detail(self, symbol: str) -> Dict:
        """生成模拟股票详情数据"""
        print(f"使用模拟股票详情数据: {symbol}")
        
        base_price = np.random.uniform(8, 50)
        
        return {
            'symbol': symbol,
            'name': self._get_stock_name(symbol),
            'last_price': round(base_price, 2),
            'open': round(base_price * 0.99, 2),
            'high': round(base_price * 1.03, 2),
            'low': round(base_price * 0.97, 2),
            'pre_close': round(base_price * 0.98, 2),
            'volume': np.random.randint(1000000, 50000000),
            'amount': round(base_price * np.random.randint(1000000, 50000000), 2),
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'bid_prices': [round(base_price - i * 0.01, 2) for i in range(1, 6)],
            'ask_prices': [round(base_price + i * 0.01, 2) for i in range(1, 6)],
            'bid_volumes': [np.random.randint(100, 1000) * 100 for _ in range(5)],
            'ask_volumes': [np.random.randint(100, 1000) * 100 for _ in range(5)],
        }

# 创建全局数据服务实例
data_service = DataService()
